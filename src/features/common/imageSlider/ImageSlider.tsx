import React, { useEffect, useRef, useState } from "react";
import styles from "./ImageSlider.module.scss";
import isAbsoluteURL from "@utils/isAbsoluteURL";
import useAppRouter from "../router.context";
import { Skeleton } from "@mui/material";
import gsap from 'gsap';

const MAX_VISIBLE_SEGMENTS = 3;
const AUTOPLAY_DELAY = 3000; // Define a constant for the autoplay delay

const ImageSlider = ({ images }: any) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [previousIndex, setPreviousIndex] = useState(0);
  const [isFading, setIsFading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const bannerRef = useRef(null);
  const stopAutoplayRef = useRef(false);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const progressRef = useRef<number>(0); // Track progress from 0-100%
  const lastTickRef = useRef<number>(Date.now()); // Last time we updated progress

  const {
    router,
    state: { locale }
  } = useAppRouter();

  const segmentWidth =
    images.length <= MAX_VISIBLE_SEGMENTS
      ? 100 / images.length
      : 100 / MAX_VISIBLE_SEGMENTS;

  useEffect(() => {
    if (!isPaused && !stopAutoplayRef.current) {
      startAutoplay();
    }
    return () => clearAutoplay();
  }, [currentIndex, isPaused]);

  useEffect(() => {
    if (isLoaded) {
      const banner = bannerRef.current;

      gsap.set(banner, {
        transformOrigin: 'top center',
        rotationX: -30,
      });

      gsap.to(banner, {
        rotationX: 0,
        duration: 2.2,
        ease: 'power4.out',
      });
    }
  }, [isLoaded]);

  useEffect(() => {
    setCurrentIndex(0)
    if (!images || images.length === 0) return;

    let loadedCount = 0;

    images.forEach((image: any) => {
      const img = new Image();
      img.src = image.imageWebp;
      img.onload = () => {
        loadedCount += 1;
        if (loadedCount === images.length) {
          startTimeRef.current = Date.now();
          setIsLoaded(true);
        }
      };
      img.onerror = () => {
        loadedCount += 1;
        if (loadedCount === images.length) {
          startTimeRef.current = Date.now(); 
          setIsLoaded(true);
        }
      };
    });
  }, [images]);

  const startAutoplay = () => {
    clearAutoplay();
    lastTickRef.current = Date.now();
    
    const remainingTime = (1 - progressRef.current / 100) * AUTOPLAY_DELAY;
    
    timeoutRef.current = setTimeout(() => {
      if (!isPaused && !stopAutoplayRef.current) {
        nextImage();
      }
    }, remainingTime);
    
    // Start progress tracking interval
    const progressInterval = setInterval(() => {
      if (!isPaused && !stopAutoplayRef.current) {
        const now = Date.now();
        const elapsed = now - lastTickRef.current;
        lastTickRef.current = now;
        
        const increment = (elapsed / AUTOPLAY_DELAY) * 100;
        progressRef.current = Math.min(progressRef.current + increment, 100);
        
        // Force re-render to update progress bar
        setIsPaused(prev => prev);
      }
    }, 50); // Update progress ~20 times per second
    
    return () => clearInterval(progressInterval);
  };

  const clearAutoplay = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
  };

  const updateIndex = (index: number) => {
    if (index === currentIndex) return;

    // Reset progress when changing slides
    progressRef.current = 0;
    lastTickRef.current = Date.now();

    setPreviousIndex(currentIndex);
    setCurrentIndex(index);
    setIsFading(true);

    setTimeout(() => {
      setIsFading(false);
    }, 250);
  };

  const nextImage = () => {
    updateIndex((currentIndex + 1) % images.length);
  };

  if (!isLoaded || !images || images.length === 0) {
    return <Skeleton variant="rectangular" width={384} height={450} sx={{ borderRadius: '24px' }} />
  }

  return (
    <div className={styles.slider} ref={bannerRef} style={{ perspective: 1000 }}
      onMouseEnter={() => {
        setIsPaused(true);
        clearAutoplay();
      }}
      onMouseLeave={() => {
        setIsPaused(false);
        startAutoplay();
      }}>
      <div className={styles["progress-container"]}>
        {images.map((image: any, index: any) => (
          <div
            key={`segment-${index}`}
            className={`${styles["bar-segment"]} ${index === currentIndex ? styles["active"] : ""
              }`}
            style={{ width: `${segmentWidth}%` }}
            onClick={() => {
              // This directly navigates to the clicked segment's image
              // updateIndex already handles resetting startTimeRef and remainingTimeRef
              updateIndex(index);
              startAutoplay(); // Resume autoplay from the new slide
            }}
          >
            {index === currentIndex && (
              <div className={`${locale === 'en' ? styles["progress-bar"] : styles["progress-bar-arabic"]} ${isPaused ? styles["paused"] : ""}`} />
            )}
          </div>
        ))}
      </div>

      <div className={styles["image-container"]}>
        <img
          src={images && images.length && images[previousIndex]['imageWebp']}
          alt={`Previous Slide`}
          className={`${styles["main-image"]} ${isFading ? styles["fade-out"] : styles["hidden"]}`}
        />
        <img
          onClick={() => {
            stopAutoplayRef.current = true;
            clearAutoplay();
            setIsPaused(true);

            const url = images?.[currentIndex]?.url;
            if (isAbsoluteURL(url)) {
              location.href = url;
            } else {
              router.push(url);
            }
          }}
          src={images && images.length && images[currentIndex]['imageWebp']}
          alt={`Slide ${currentIndex + 1}`}
          className={`${styles["main-image"]} ${!isFading ? styles["fade-in"] : styles["hidden"]}`}
        />
      </div>
    </div>
  );
};

export default ImageSlider;
